# 卡片组件重构说明

## 概述

为了提高代码的可维护性和复用性，我们将Home.vue和RequirementsList.vue中的卡片组件抽取成了独立的组件。

## 组件列表

### 1. TaskCard.vue - 任务卡片组件

从Home.vue中抽取的任务卡片组件，用于显示任务信息。

#### Props
- `task` (Object, required): 任务对象

#### Slots
- `actions`: 操作按钮插槽，提供task对象作为参数

#### 使用示例
```vue
<task-card :task="task">
  <template #actions="{ task }">
    <el-button @click="handleAction(task)">操作</el-button>
  </template>
</task-card>
```

#### 支持的按钮样式类
- `.btn-primary`: 主要按钮（蓝色）
- `.btn-outline`: 边框按钮（灰色边框）
- `.btn-success`: 成功按钮（绿色）
- `.btn-danger`: 危险按钮（红色）

### 2. RequirementCard.vue - 需求卡片组件

从RequirementsList.vue中抽取的需求卡片组件，用于显示需求信息。

#### Props
- `requirement` (Object, required): 需求对象

#### Events
- `view`: 点击卡片时触发，传递requirement对象

#### Slots
- `actions`: 操作按钮插槽，提供requirement对象作为参数

#### 使用示例
```vue
<requirement-card 
  :requirement="requirement" 
  @view="handleView"
>
  <template #actions="{ requirement }">
    <button class="action-btn action-view" @click="viewRequirement(requirement)">查看</button>
    <button class="action-btn action-claim" @click="claimRequirement(requirement)">认领</button>
  </template>
</requirement-card>
```

#### 支持的按钮样式类
- `.action-btn`: 基础操作按钮样式
- `.action-view`: 查看按钮（蓝色）
- `.action-claim`: 认领按钮（绿色）
- `.action-submit`: 提交按钮（橙色）
- `.action-withdraw`: 撤回按钮（灰色）
- `.action-approve`: 通过按钮（绿色）
- `.action-reject`: 驳回按钮（红色）

## 重构收益

1. **代码复用**: 卡片组件可以在多个页面中复用
2. **维护性**: 卡片相关的逻辑和样式集中管理
3. **可扩展性**: 通过插槽机制，可以灵活定制操作按钮
4. **一致性**: 确保所有页面的卡片样式和行为保持一致

## 迁移说明

### Home.vue 变更
- 移除了task-card相关的模板代码和样式
- 移除了formatDate和getStatusBadgeClass方法
- 使用TaskCard组件替代原有的卡片实现

### RequirementsList.vue 变更
- 移除了requirement-card相关的模板代码和样式
- 移除了卡片样式相关的方法（getCardHeaderClass、getPriorityClass等）
- 使用RequirementCard组件替代原有的卡片实现

## 注意事项

1. 组件内部已包含所有必要的样式，无需在父组件中重复定义
2. 按钮样式通过:deep()选择器支持，确保样式能够正确应用
3. 日期格式化等工具方法已内置在组件中
4. 组件支持响应式设计，在不同屏幕尺寸下都能正常显示
