<template>
  <div class="task-card">
    <div class="task-header">
      <div>
        <div class="task-title">{{ task.title || task.requirementName }}</div>
        <div class="task-project">项目：{{ task.project_name || task.projectName }}</div>
        <div class="user-assign">
          <template v-if="!task.developers || task.developers.length === 0">
            负责人：未分配
          </template>
          <template v-else>
            开发者：{{ Array.isArray(task.developers) ? task.developers.map(d => d.name).join(', ') : task.developers }}
          </template>
        </div>
        <div class="tester-assign">
          <template v-if="!task.testers || task.testers.length === 0">
            测试者：未分配
          </template>
          <template v-else>
            测试者：{{ Array.isArray(task.testers) ? task.testers.map(t => t.name).join(', ') : task.testers }}
          </template>
        </div>
      </div>
      <div class="task-meta">
        <span class="badge" :class="getStatusBadgeClass(task.status)">{{ task.status }}</span>
        <div class="task-deadline">截止日期：{{ formatDate(task.end_date || task.endDate) }}</div>
      </div>
    </div>
    <div class="task-footer">
      <div class="task-date">
        <el-icon><Clock /></el-icon>
        创建于 {{ formatDate(task.submit_time || task.submitTime) }}
        <template v-if="task.submitter_name || task.submitterName">
          by {{ task.submitter_name || task.submitterName }}
        </template>
      </div>
      <div class="task-actions">
        <slot name="actions" :task="task"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Clock } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  task: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // 如果无法解析，返回原始字符串

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '-');
};

// 获取状态对应的徽章样式
const getStatusBadgeClass = (status) => {
  switch (status) {
    case '待处理':
      return 'badge-purple';
    case '开发中':
      return 'badge-blue';
    case '测试中':
      return 'badge-orange';
    case '验证中':
      return 'badge-green';
    case '已完成':
      return 'badge-success';
    default:
      return 'badge-default';
  }
};
</script>

<style scoped>
.task-card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-medium);
  padding: 20px;
  transition: all 0.3s;
}

.task-card:hover {
  box-shadow: var(--shadow-lg);
}

.task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-medium);
}

.task-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.task-project {
  font-size: 13px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.task-meta {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.task-deadline {
  margin-top: 8px;
  font-size: 13px;
  color: var(--text-hint);
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
}

.badge-blue {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.badge-orange {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-purple {
  background-color: var(--purple-light);
  color: var(--purple);
}

.badge-green {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-success {
  background-color: #e6f7ff;
  color: #1890ff;
}

.badge-default {
  background-color: #f5f5f5;
  color: #666;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-medium);
  border-top: 1px solid var(--border-color);
}

.task-date {
  font-size: 13px;
  color: var(--text-hint);
  display: flex;
  align-items: center;
}

.task-date .el-icon {
  margin-right: 4px;
  opacity: 0.5;
  font-size: 16px;
}

.task-actions {
  display: flex;
  gap: var(--spacing-small);
}

.user-assign {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.tester-assign {
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  margin-top: 4px;
}
</style>
